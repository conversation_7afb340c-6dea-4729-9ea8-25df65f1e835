from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id

# -----------------------------------------------------------------------------
# Perform folder search
# -----------------------------------------------------------------------------


async def _perform_folder_search(
    query: str,
    index_name: str,
    folder_path: str,
    limit: int = 10,
    is_local: bool = False,
) -> list[dict[str, Any]]:
    """
    Perform a search within a specific folder using Qdrant.
    Args:
        query: Search query string
        index_name: Name of the collection to search in
        folder_path: Path to folder to search within
        limit: Maximum number of results to return
    Returns:
        List of search results with chunks and metadata
    """
    qc = get_db_client()
    embedding_backend = EmbeddingInferenceBuilder.create(is_local)
    try:
        search_vectors = await embedding_backend.generate(query)
        folder_filter = Filter(
            must=[
                FieldCondition(
                    key="metadata.file",
                    match=MatchText(text=folder_path),
                )
            ]
        )
        search_results = qc.search(
            collection_name=index_name,
            query_vector=("vectors", search_vectors),  # type: ignore
            limit=limit,
            query_filter=folder_filter,
        )
        if not search_results:
            return []
        results = []
        for result in search_results:
            payload = result.payload["metadata"]
            results.append(
                {
                    "file": payload["file"],
                    "content": {"text": payload["content"]},
                    "additional_metadata": payload["additional_metadata"],
                }
            )
        return results
    except Exception as e:
        LOGGER.error(f"Error in perform_folder_search: {e}")
        raise


# -----------------------------------------------------------------------------
# Process folder search
# -----------------------------------------------------------------------------
async def process_folder_search(
    *,
    query: str,
    tool_id: str,
    folder_path: str,
    index_name: str,
    search_references: SearchReferences,
):
    """Handle folder search actions"""
    LOGGER.info(
        f"Performing folder search for Query: {query}, Folder Path: {folder_path}, Index Name: {index_name}"
    )
    try:
        search_results = await _perform_folder_search(
            query=query,
            index_name=index_name,
            folder_path=folder_path,
        )
        for chunk in search_results:
            text = chunk["content"]["text"]
            _filename = os.path.basename(chunk["file"])
            _filepath = chunk["file"]
            search_references.add_search_result(
                path=_filepath, name=_filename, content=text, type="file"
            )

        action_arguments = json.dumps({"query": query, "folder_path": folder_path})

        return new_messages, search_references
    except Exception as e:
        LOGGER.error(f"Error in process_folder_search: {e}")
        raise


# -----------------------------------------------------------------------------
