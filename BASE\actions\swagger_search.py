from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id



def query_api(query, results):
    # ["<endpoint> <path></path> <summary></summary> </endpoint>"]
    results_xml: list[str] = []
    for result in results:
        metadata = result.payload["metadata"]
        endpoint_str = "<endpoint>\n"
        endpoint_str += f"  <path>{metadata['name']}</path>\n"
        endpoint_str += f"  <summary>{metadata['content']}</summary>\n"
        endpoint_str += "</endpoint>"
        results_xml.append(endpoint_str)
    with open("coupled_results_str.xml", "w") as f:
        f.write("\n".join(results_xml))

        message_content = f"""
Following are the search results from a swagger API spec. Your task is to select the most relevant endpoints paths based on the provided query.

<query>{query}</query>
<data>{results_xml}</data>

Expected output format:
<paths>
path1
path2
path3
</paths>

If the search results are not relevant to the query, return the paths as:
<paths>
</paths>

STRICT INSTRUCTION:
When returning the paths, make sure to return the exact paths as provided. Do not add any other paths.
YOU MUST PROVIDE AT LEAST 2 PATHS IRRESPECTIVE OF THE QUERY.
"""

    base_url = G_BASE_URL.get().general

    response = requests.post(
        f"{base_url}/swagger/search",
        json={"content": message_content},
    )
    if response.status_code != 200:
        raise ValueError()

    response_text = response.text.replace("\\n", "\n")
    paths_begin = response_text.find("<paths>")
    paths_end = response_text.find("</paths>")
    paths = response_text[paths_begin + 7 : paths_end].split("\n")
    paths = [path.strip() for path in paths if path.strip()]

    return paths


async def _perform_swagger_search(
    query: str,
    index_name: str,
    limit: int = 20,
    query_filter: Optional[rest.Filter] = None,
) -> list[dict[str, Any]]:
    qc = get_db_client()
    embeddings_backend = EmbeddingInferenceBuilder().create()

    try:
        query_embeddings = await embeddings_backend.generate(query)
        results = qc.search(
            collection_name=index_name,
            query_vector=("vectors", query_embeddings),
            limit=limit,
            with_payload=True,
            query_filter=query_filter,
        )

        # name -> swagger endpoint spec
        results_map: dict[str, dict[str, Any]] = {}
        for result in results:
            metadata = result.payload["metadata"]
            results_map[metadata["name"]] = metadata["additional_metadata"]

        LOGGER.info(f"Keys in results_map: {list(results_map.keys())}")
        api_results = query_api(query=query, results=results)
        LOGGER.info(f"API results: {api_results}")
        filtered_results = list(map(lambda x: results_map[x], api_results))
        return filtered_results
    except Exception as e:
        LOGGER.error(f"Error is performing swagger_search: {e}")
        raise


async def process_swagger_search(
    *, query: str, tool_id: str, kbid: str, search_refrences: SearchReferences
):
    LOGGER.info(f"Performing swagger search for query: {query}")

    try:
        search_results = await _perform_swagger_search(query=query, index_name=kbid)
        action_arguments = {"query": query, "kbid": kbid}
        for results in search_results:
            search_refrences.add_search_result(
                type="file",
                name=results["metadata"]["name"],
                path=results["metadata"]["file"],
                content=results["metadata"]["additional_metadata"],
            )

        final_content = json.dumps(search_results)
        with open("swagger_search_results.json", "w") as f:
            f.write(final_content)

        return [
            {"role": "tool", "name": tool_id, "content": final_content}
        ], search_refrences

    except Exception as e:
        LOGGER.error(f"Error in process_context_search: {e}")
        raise
