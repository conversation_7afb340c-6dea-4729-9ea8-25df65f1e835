

# -----------------------------------------------------------------------------
# Perform context search
# -----------------------------------------------------------------------------


async def _perform_remote_search(*, query: str, kbid: str) -> list[dict[str, Any]]:
    """
    Perform a remote vector search using Qdrant.
    """
    LOGGER.info(f"Performing remote search for Query: {query}, KBID: {kbid}")
    try:
        session = G_SESSION_ID.get()
        if not session:
            LOGGER.warning("No session found, skipping remote search")
            return []

        LOGGER.info(f"STARTING REMOTE SEARCH WITH QUERY: {query} AND KBID: {kbid}")
        async with httpx.AsyncClient(verify=SSL_CONTEXT) as client:
            response = await client.post(
                "https://codebase.v3.codemate.ai/context_search",
                json={"query": query, "kbid": kbid},
                headers={"x-session": session},
            )
            results = response.json()
            LOGGER.info(f"Remote search results: {results}")
            results = list(
                map(
                    lambda x: {
                        "file": x.get("file", ""),
                        "content": {"text": x.get("content", "")},
                        "additional_metadata": {
                            "line_start": (
                                x.get("lines", [])[0]
                                if len(x.get("lines", [])) > 0
                                else None
                            ),
                            "line_end": (
                                x.get("lines", [])[1]
                                if len(x.get("lines", [])) > 1
                                else None
                            ),
                        },
                    },
                    results,
                )
            )
            LOGGER.info(f"Remote mapped search results: {results}")
            return results
    except Exception as e:
        LOGGER.error(f"Error in perform_remote_search: {e}")
        return []


async def _perform_qdrant_search(
    *,
    query: str,
    index_name: str,
    local_embedding_inference: bool = False,
    limit: int = 30,
    query_filter: Optional[rest.Filter] = None,
) -> list[dict[str, Any]]:
    """
    Perform a local vector search using Qdrant.
    """
    qc = get_db_client()
    embeddings_backend = EmbeddingInferenceBuilder().create(local_embedding_inference)

    try:
        search_vectors = await embeddings_backend.generate(query)
        search_results = qc.search(
            collection_name=index_name,
            query_vector=("vectors", search_vectors),  # type: ignore
            limit=limit,
            query_filter=query_filter,
        )
        scores = list(map(lambda x: x.score, search_results))
        print(f"Search Scores: {sum(scores) / len(scores)}")
        if not search_results:
            return []

        results = []
        for result in search_results:
            payload = result.payload["metadata"]
            results.append(
                {
                    "file": payload["file"],
                    "content": {"text": payload.get("content", str(payload))},
                    "additional_metadata": payload["additional_metadata"],
                }
            )
        return results
    except Exception as e:
        LOGGER.trace(f"Error in perform_qdrant_search: {e}")
        raise


async def _perform_local_search(
    query: str,
    kbid: str,
    *,
    limit: int = 30,
    query_filter: Optional[rest.Filter] = None,
    local_embedding_inference: bool = False,
) -> list[dict[str, Any]]:
    """
    Perform a local vector search using Qdrant with intelligent knowledge base routing.

    This function implements intelligent routing logic:
    1. First checks if the knowledge base exists locally using QdrantKnowledgeBase.exists_id()
    2. If found locally, performs local search (on_cloud=False)
    3. If not found locally, performs cloud/shared search (on_cloud=True)

    Args:
        query: Search query string
        kbid: Knowledge base ID to search in
        limit: Maximum number of results to return
        query_filter: Optional filter for the search
        local_embedding_inference: Whether to use local embedding inference

    Returns:
        List of search results with chunks and metadata
    """

    # Intelligent knowledge base routing logic
    # First, check if the knowledge base exists locally
    kb_exists_locally = QdrantKnowledgeBase.exists_id(kbid)

    if kb_exists_locally:
        # Knowledge base found locally - perform local search
        on_cloud = False
        search_kbid = kbid
        LOGGER.info(f"Knowledge base {kbid} found locally - performing local search")
    else:
        # Knowledge base not found locally - perform cloud/shared search
        on_cloud = True
        search_kbid = kbid
        LOGGER.info(f"Knowledge base {kbid} not found locally - performing cloud search")

    LOGGER.info(
        f"Performing search for Query: {query}, KBID: {search_kbid}, On Cloud: {on_cloud}, Local Embedding: {local_embedding_inference}"
    )

    try:
        if on_cloud:
            return await _perform_remote_search(query=query, kbid=search_kbid)
        else:
            return await _perform_qdrant_search(
                index_name=search_kbid,
                query=query,
                limit=limit,
                local_embedding_inference=local_embedding_inference,
                query_filter=query_filter,
            )
    except Exception as e:
        LOGGER.trace(f"Error in perform_local_search: {e}")
        return []


# -----------------------------------------------------------------------------
# Process context search
# -----------------------------------------------------------------------------


async def process_context_search(
    *, query: str, tool_id: str, kbid: str, search_references: SearchReferences
) -> tuple[list[dict[str, Any]], SearchReferences]:
    """Handle context search actions"""
    LOGGER.info(f"Performing context search for Query: {query}, KBID: {kbid}")

    try:
        search_results = await _perform_local_search(
            query=query, local_embedding_inference=False, kbid=kbid
        )
        action_arguments = {"query": query, "kbid": kbid}

        LOGGER.info(f"Search results: {search_results}")

        for chunk in search_results:
            text = chunk["content"]["text"]
            _filename = os.path.basename(chunk["file"])
            _filepath = chunk["file"]
            search_references.add_search_result(
                path=_filepath, name=_filename, content=text, type="file"
            )


        return new_messages, search_references
    except Exception as e:
        LOGGER.error(f"Error in process_context_search: {e}")
        raise


# -----------------------------------------------------------------------------
